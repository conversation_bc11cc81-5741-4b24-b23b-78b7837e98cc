#!/bin/bash

echo "🚀 MemO AI Setup Script"
echo "========================"

# Check if Java 17+ is installed
echo "Checking Java version..."
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$java_version" -lt 17 ]; then
    echo "❌ Java 17 or higher is required. Current version: $java_version"
    echo "Please install Java 17+ and try again."
    exit 1
else
    echo "✅ Java version: $(java -version 2>&1 | head -n 1)"
fi

# Check if <PERSON>ven is installed
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed. Please install <PERSON><PERSON> and try again."
    exit 1
else
    echo "✅ Maven version: $(mvn -version | head -n 1)"
fi

# Check if Docker is installed (optional)
if command -v docker &> /dev/null; then
    echo "✅ Docker is available"
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker not found. You can still run the application locally."
    DOCKER_AVAILABLE=false
fi

# Create uploads directory
echo "Creating uploads directory..."
mkdir -p uploads

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file template..."
    cat > .env << EOF
# OpenRouter Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mem0 Configuration
MEMO_API_KEY=your_memo_api_key_here

# Database Configuration (optional, defaults in application.yml)
DB_URL=****************************************
DB_USERNAME=memo_user
DB_PASSWORD=memo_password
EOF
    echo "⚠️  Please edit .env file with your API keys before running the application."
else
    echo "✅ .env file already exists"
fi

echo ""
echo "🎉 Setup complete!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your API keys"
echo "2. Set up PostgreSQL database (see README.md)"
echo "3. Run the application:"
echo "   - Local: mvn spring-boot:run"
echo "   - Docker: docker-compose up"

if [ "$DOCKER_AVAILABLE" = true ]; then
    echo ""
    echo "To run with Docker Compose:"
    echo "docker-compose up -d"
fi
